import { Inject, Injectable } from '@nestjs/common';
import { eq, ilike, and, inArray, sql } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

import { formatTag } from '@/utils/tags';

import { tags } from '@/db/schema/tags';
import * as schema from '@/db/schema';

import { TagDto } from './dto/tag.dto';

import { TagStatus } from '@/constants/tags';

@Injectable()
export class TagsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async searchTags(query?: string, limit: number = 10): Promise<TagDto[]> {
    const conditions = [eq(tags.status, TagStatus.ACTIVE)];

    if (query) {
      conditions.push(ilike(tags.name, `%${query}%`));
    }

    const results = await this.db
      .select({
        id: tags.id,
        name: tags.name,
      })
      .from(tags)
      .where(and(...conditions))
      .limit(limit);

    return results;
  }

  async createMissingTags(
    tagNames: string[],
    tx?: PostgresJsDatabase<typeof schema>,
  ): Promise<TagDto[]> {
    // Use provided transaction or default to db instance
    const queryRunner = tx || this.db;

    // Handle empty array case
    if (!tagNames || tagNames.length === 0) {
      return [];
    }

    // Create a map to preserve formatted casing
    const formattedTagsMap = new Map<string, string>();

    // Store formatted case but use lowercase for duplicate checking
    tagNames.forEach((name) => {
      const trimmedName = name.trim();
      if (trimmedName) {
        const formattedName = formatTag(trimmedName);
        formattedTagsMap.set(formattedName.toLowerCase(), formattedName);
      }
    });

    // Get unique lowercase tag names for checking duplicates
    const lowercaseNames = [...formattedTagsMap.keys()];

    // Find existing tags with these names (both active and inactive)
    const existingTags = await queryRunner
      .select({
        id: tags.id,
        name: tags.name,
        status: tags.status,
      })
      .from(tags)
      .where(inArray(sql`lower(${tags.name})`, lowercaseNames));

    type inferTagType = typeof existingTags;

    // Separate active and inactive tags
    const activeTags: inferTagType = [];
    const inactiveTags: inferTagType = [];

    // Create a map of lowercase name to existing tag
    const existingTagsMap = new Map<string, (typeof existingTags)[number]>();

    existingTags.forEach((tag) => {
      const lowerName = tag.name.toLowerCase();
      existingTagsMap.set(lowerName, tag);

      if (tag.status === TagStatus.ACTIVE) {
        activeTags.push(tag);
      } else if (tag.status === TagStatus.INACTIVE) {
        inactiveTags.push(tag);
      }
    });

    // Update inactive tags to active
    if (inactiveTags.length > 0) {
      await queryRunner
        .update(tags)
        .set({ status: TagStatus.ACTIVE })
        .where(
          inArray(
            tags.id,
            inactiveTags.map((tag) => tag.id),
          ),
        );
    }

    // Find which names don't exist at all
    const newTagsToCreate: string[] = [];

    lowercaseNames.forEach((lowerName) => {
      if (!existingTagsMap.has(lowerName)) {
        // Use the formatted version for creating new tags
        newTagsToCreate.push(formattedTagsMap.get(lowerName)!);
      }
    });

    // Create completely new tags
    let newTags: TagDto[] = [];
    if (newTagsToCreate.length > 0) {
      newTags = await queryRunner
        .insert(tags)
        .values(
          newTagsToCreate.map((name) => ({
            name,
            status: TagStatus.ACTIVE,
          })),
        )
        .returning({
          id: tags.id,
          name: tags.name,
        });
    }

    // return result;
    return [...existingTags, ...newTags];
  }
}
