export enum EntityName {
  USER = 'user',
  ROLE = 'role',
  PERMISSION = 'permission',
  WORKSPACE = 'workspace',
  WORKSPACE_USER = 'workspace user',
  ORGANISATION = 'organisation',
  MODULE = 'module',
  USER_ROLES = 'user role',
  ROLE_PERMISSION = 'role permission',
  USER_PERMISSION = 'user permission',
  POST = 'post',
  POST_MEDIA = 'post media',
  POST_LIKE = 'post like',
  POST_COMMENT = 'post comment',
  POST_VIEW = 'post view',
  OPPORTUNITY = 'opportunity',
  OPPORTUNITY_ATTACHMENT = 'opportunity attachment',
  SUBTYPE = 'subtype',
  CATEGORY = 'category',
  SUBSCRIPTION = 'subscription',
  FOLLOWER = 'follower',
  POST_POLL = 'post poll',
  EMPLOYER = 'employer',
  CONNECTION = 'connection',
  PRESTIGE_MEMBERSHIP_APPLICATION = 'prestige membership application',
}
